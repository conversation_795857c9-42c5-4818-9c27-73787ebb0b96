# work-order-pc/page 接口详细说明文档

## 接口概述

**接口路径**: `/work-order-pc/page`  
**请求方式**: POST  
**接口作用**: 维修工单-付款维修单列表（PC端分页查询）  
**接口描述**: 用于PC端管理系统查询维修工单列表，支持多维度条件筛选和分页展示

## 控制器层实现

### 控制器位置
- **文件路径**: `src/main/java/com/hightop/benyin/work/order/api/controller/WorkOrderPcController.java`
- **控制器类**: `WorkOrderPcController`
- **方法**: `page(@RequestBody WorkOrderPcPageQuery pageQuery)`

### 控制器代码逻辑
```java
@PostMapping("/page")
@ApiOperation("维修工单-付款维修单列表")
@IgnoreOperationLog
public RestResponse<DataGrid<WorkOrderPcListVo>> page(@RequestBody WorkOrderPcPageQuery pageQuery) {
    return RestResponse.ok(this.workOrderService.pcPage(pageQuery));
}
```

## 服务层实现

### 服务类位置
- **文件路径**: `src/main/java/com/hightop/benyin/work/order/application/service/WorkOrderService.java`
- **方法**: `pcPage(WorkOrderPcPageQuery query)`

### 核心实现逻辑

#### 1. 多表关联查询
使用MyBatis-Plus的MPJLambdaWrapper构建复杂的多表关联查询：

```java
MPJLambdaWrapper<Object> wrapper = MPJWrappers.lambdaJoin()
    .selectAll(WorkOrder.class)  // 选择工单表所有字段
    .selectAs(Customer::getName, WorkOrderPcListVo::getCustomerName)  // 客户名称
    .selectAs(Customer::getSubbranch, WorkOrderPcListVo::getSubbranch)  // 分店
    .selectAs(Customer::getSeqId, WorkOrderPcListVo::getCustomerSeq)  // 客户编码
    .selectAs(Customer::getLocation, WorkOrderPcListVo::getLocation)  // 位置信息
    .selectAs(CustomerDeviceGroup::getDeviceGroup, WorkOrderPcListVo::getDeviceGroup)  // 设备组
    .selectAs(CustomerStaff::getTel, WorkOrderPcListVo::getPhone)  // 联系电话
    .innerJoin(CustomerDeviceGroup.class, CustomerDeviceGroup::getId, WorkOrder::getDeviceGroupId)
    .leftJoin(Customer.class, Customer::getId, WorkOrder::getCustomerId)
    .leftJoin(CustomerStaff.class, CustomerStaff::getId, WorkOrder::getCustomerStaffId);
```

#### 2. 条件筛选处理
调用`addCondition(query, wrapper)`方法添加查询条件

#### 3. 排序规则
按工单创建时间倒序排列：`wrapper.orderByDesc(WorkOrder::getCreatedAt)`

#### 4. 分页查询执行
使用PageHelper进行分页查询，并对结果进行后处理

#### 5. 产品信息补充
对每个工单记录补充产品信息：
```java
.peek(p -> {
    ProductTreeDto info = this.productTreeDomainService.getFullProductTree(p.getProductId());
    p.setProductInfo(String.format("%s/%s", info.getBrand(), info.getMachine()));
})
```

## 请求参数详解

### 请求参数类
- **类名**: `WorkOrderPcPageQuery`
- **继承**: `PageQuery`（包含分页参数）

### 查询条件字段

| 字段名 | 类型 | 描述 | 查询方式 |
|--------|------|------|----------|
| status | List<WorkOrderStatus> | 工单状态列表 | IN查询 |
| serTypes | List<String> | 合约类型列表 | IN查询 |
| serType | String | 单个合约类型 | 等值查询 |
| code | String | 工单编号 | 模糊查询 |
| customerId | Long | 客户ID | 等值查询 |
| customerName | String | 客户店招名 | 模糊查询 |
| customerSeq | String | 客户编码 | 模糊查询 |
| deviceGroup | String | 设备组 | 模糊查询 |
| phone | String | 手机号 | 模糊查询 |
| errorCode | String | 故障码 | 模糊查询 |
| productIds | Set<Long> | 机型ID集合 | IN查询 |
| regionPath | String | 所属省市区 | - |
| engineerId | UserEntry | 维修工程师 | 等值查询 |
| createTimeStart | String | 下单时间-开始 | 大于等于查询 |
| createTimeEnd | String | 下单时间-结束 | 小于等于查询 |
| completeTimeStart | String | 完成时间-开始 | 大于等于查询 |
| completeTimeEnd | String | 完成时间-结束 | 小于等于查询 |
| isEvaluated | Boolean | 是否评价 | 等值查询 |
| sendReportTimeStart | String | 提交报告时间-开始 | 大于等于查询 |
| sendReportTimeEnd | String | 提交报告时间-结束 | 小于等于查询 |
| startMonth | String | 开始月份 | - |
| endMonth | String | 结束月份 | - |
| payMode | String | 支付方式 | 等值查询 |

## 返回结果详解

### 返回数据结构
- **类型**: `RestResponse<DataGrid<WorkOrderPcListVo>>`
- **说明**: 标准分页响应结构，包含分页信息和数据列表

### 返回字段说明

#### WorkOrderPcListVo 字段详解

| 字段名 | 类型 | 描述 | 数据来源 |
|--------|------|------|----------|
| **基础工单信息** | | | |
| id | Long | 工单ID | work_order.id |
| code | String | 工单编码 | work_order.code |
| status | WorkOrderStatus | 工单状态 | work_order.status |
| createdAt | LocalDateTime | 创建时间 | work_order.created_at |
| updatedAt | LocalDateTime | 更新时间 | work_order.updated_at |
| **客户相关信息** | | | |
| customerId | Long | 客户ID | work_order.customer_id |
| customerName | String | 客户名称 | b_customer.name |
| customerSeq | String | 客户编码 | b_customer.seq_id |
| subbranch | String | 分店 | b_customer.subbranch |
| location | LbsLocation | 位置信息 | b_customer.location |
| **联系人信息** | | | |
| customerStaffId | Long | 报修人ID | work_order.customer_staff_id |
| phone | String | 联系电话 | b_customer_staff.tel |
| **设备相关信息** | | | |
| deviceGroupId | Long | 设备组ID | work_order.device_group_id |
| deviceGroup | DictItemEntry | 设备组名称 | b_customer_device_group.device_group |
| productId | Long | 产品ID | work_order.product_id |
| productInfo | String | 品牌+机型 | 通过ProductTreeDto组装 |
| machineNum | String | 机器编码 | work_order.machine_num |
| **故障信息** | | | |
| errorCode | String | 故障码 | work_order.error_code |
| excDesc | String | 问题描述 | work_order.exc_desc |
| excPics | CosObjectList | 故障图片 | work_order.exc_pics |
| **工程师信息** | | | |
| engineerId | UserEntry | 接单工程师ID | work_order.engineer_id |
| engineer | UserEntry | 接单工程师详情 | 字典绑定 |
| **费用信息** | | | |
| totalPay | Long | 总费用 | work_order.total_pay |
| totalAmount | Long | 总金额 | work_order.total_amount |
| laborAmount | Long | 人工费 | 计算字段 |
| itemPay | Long | 配件费 | work_order.item_pay |
| additionalPay | Long | 追加报酬 | work_order.additional_pay |
| **合约信息** | | | |
| serType | SerTypeEnums | 合约类型 | work_order.ser_type |
| treatyType | DictItemEntry | 合约类型字典 | b_customer_device_group.treaty_type |
| contractCode | String | 质保合同编号 | work_order.contract_code |
| payMode | PayModeEnum | 支付方式 | work_order.pay_mode |
| **时间信息** | | | |
| expectArriveTime | LocalDateTime | 期望到店时间 | work_order.expect_arrive_time |
| orderReceiveTime | LocalDateTime | 接单时间 | work_order.order_receive_time |
| completedAt | LocalDateTime | 完成时间 | work_order.completed_at |
| sendReportTime | LocalDateTime | 提交报告时间 | work_order.send_report_time |
| **其他信息** | | | |
| isEvaluated | Boolean | 是否评价 | work_order.is_evaluated |
| currentProcess | RepairProcessEnum | 当前维修进度 | work_order.current_process |

## 数据源表说明

### 主表：work_order（工单表）
- **表名**: `work_order`
- **主键**: `id`
- **描述**: 存储维修工单的核心信息

### 关联表详解

#### 1. b_customer（客户表）
- **关联方式**: LEFT JOIN
- **关联条件**: `b_customer.id = work_order.customer_id`
- **提供字段**: 客户名称、客户编码、分店、位置信息等

#### 2. b_customer_device_group（客户设备组表）
- **关联方式**: INNER JOIN
- **关联条件**: `b_customer_device_group.id = work_order.device_group_id`
- **提供字段**: 设备组名称、合约类型等

#### 3. b_customer_staff（客户员工表）
- **关联方式**: LEFT JOIN
- **关联条件**: `b_customer_staff.id = work_order.customer_staff_id`
- **提供字段**: 联系电话、报修人姓名等

### 数据字典表
- **用户信息**: 通过UserEntry绑定获取工程师信息
- **字典项**: 通过DictItemBind注解绑定各种字典项（设备组、合约类型等）

## 查询条件处理逻辑

### addCondition方法实现
```java
private void addCondition(WorkOrderPcPageQuery query, MPJLambdaWrapper<Object> wrapper) {
    wrapper.eq(Objects.nonNull(query.getEngineerId()), WorkOrder::getEngineerId, query.getEngineerId())
            .like(StringUtils.isNotBlank(query.getCustomerName()), Customer::getName, query.getCustomerName())
            .like(StringUtils.isNotBlank(query.getCustomerSeq()), Customer::getSeqId, query.getCustomerSeq())
            .like(StringUtils.isNotBlank(query.getPhone()), CustomerStaff::getTel, query.getPhone())
            .like(StringUtils.isNotBlank(query.getCode()), WorkOrder::getCode, query.getCode())
            .like(StringUtils.isNotBlank(query.getErrorCode()), WorkOrder::getErrorCode, query.getErrorCode())
            .in(CollectionUtils.isNotEmpty(query.getProductIds()), WorkOrder::getProductId, query.getProductIds())
            .like(StringUtils.isNotBlank(query.getDeviceGroup()), CustomerDeviceGroup::getDeviceGroup, query.getDeviceGroup())
            .eq(StringUtils.isNotBlank(query.getSerType()), WorkOrder::getSerType, query.getSerType())
            .eq(StringUtils.isNotBlank(query.getPayMode()), WorkOrder::getPayMode, query.getPayMode())
            .eq(query.getCustomerId() != null, WorkOrder::getCustomerId, query.getCustomerId())
            .eq(query.getIsEvaluated() != null, WorkOrder::getIsEvaluated, query.getIsEvaluated())
            .in(CollectionUtils.isNotEmpty(query.getSerTypes()), WorkOrder::getSerType, query.getSerTypes())
            .in(CollectionUtils.isNotEmpty(query.getStatus()), WorkOrder::getStatus, query.getStatus())
            .ge(StringUtils.isNotBlank(query.getCreateTimeStart()), WorkOrder::getCreatedAt, query.getCreateTimeStart() + " 00:00:00")
            .le(StringUtils.isNotBlank(query.getCreateTimeEnd()), WorkOrder::getCreatedAt, query.getCreateTimeEnd() + " 23:59:59")
            .ge(StringUtils.isNotBlank(query.getCompleteTimeStart()), WorkOrder::getCompletedAt, query.getCompleteTimeStart() + " 00:00:00")
            .le(StringUtils.isNotBlank(query.getCompleteTimeEnd()), WorkOrder::getCompletedAt, query.getCompleteTimeEnd() + " 23:59:59")
            .ge(StringUtils.isNotBlank(query.getSendReportTimeStart()), WorkOrder::getSendReportTime, query.getSendReportTimeStart() + " 00:00:00")
            .le(StringUtils.isNotBlank(query.getSendReportTimeEnd()), WorkOrder::getSendReportTime, query.getSendReportTimeEnd() + " 23:59:59");
}
```

### 查询条件规则说明

1. **等值查询**: 工程师ID、客户ID、合约类型、支付方式、是否评价
2. **模糊查询**: 客户名称、客户编码、手机号、工单编号、故障码、设备组
3. **范围查询**: 创建时间、完成时间、提交报告时间（支持开始和结束时间）
4. **IN查询**: 工单状态列表、合约类型列表、机型ID集合
5. **时间处理**: 日期字符串自动补充时分秒（开始时间补充00:00:00，结束时间补充23:59:59）

## 性能优化说明

1. **索引优化**: 建议在常用查询字段上建立索引
2. **分页查询**: 使用PageHelper进行物理分页，避免内存溢出
3. **字段选择**: 只选择必要的字段，减少数据传输量
4. **关联查询**: 使用适当的JOIN类型，避免笛卡尔积

## 使用示例

### 请求示例
```json
{
    "pageNum": 1,
    "pageSize": 20,
    "status": ["pending", "processing"],
    "customerName": "测试客户",
    "createTimeStart": "2024-01-01",
    "createTimeEnd": "2024-01-31",
    "engineerId": {
        "id": 123,
        "name": "张工程师"
    }
}
```

### 响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 100,
        "pageNum": 1,
        "pageSize": 20,
        "pages": 5,
        "list": [
            {
                "id": 1,
                "code": "WO202401010001",
                "customerName": "测试客户",
                "customerSeq": "KH000001",
                "phone": "13800138000",
                "deviceGroup": {
                    "code": "printer",
                    "name": "打印机"
                },
                "productInfo": "惠普/LaserJet Pro",
                "status": "processing",
                "createdAt": "2024-01-01T10:00:00",
                "totalPay": 50000,
                "laborAmount": 30000
            }
        ]
    }
}
```
