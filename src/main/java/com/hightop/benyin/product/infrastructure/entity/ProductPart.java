package com.hightop.benyin.product.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 零件实体
 *
 * <AUTHOR>
 * @date 2023-10-31 10:15:38
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_product_part")
@ApiModel
public class ProductPart {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;
    /**
     * 不修改oem编号
     */
    @TableField(value = OEM, updateStrategy = FieldStrategy.NEVER)
    @ApiModelProperty("原厂零件编号(OEM)")
    @NotBlank(message = "原厂零件编号(OEM)不能为空")
    String oemNumber;
    @TableField("en")
    @ApiModelProperty("零件英文名称")
    @NotBlank(message = "零件英文名称不能为空")
    String en;
    @TableField("ch")
    @ApiModelProperty("零件中文名称")
    @NotBlank(message = "零件中文名称不能为空")
    String ch;
    @TableField("type")
    @ApiModelProperty("物品小类(字典项码)")
    @NotNull(message = "物品小类不能为空")
    @DictItemBind(StorageArticle.TYPE)
    DictItemEntry type;
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;
    @TableField("deleted")
    @TableLogic
    @ApiModelProperty("是否删除")
    Integer deleted;
    @TableField(exist = false)
    @ApiModelProperty("产品树机型id(多选机型)")
    List<Long> productIds;
    /**
     * OEM编号数据库字段
     */
    public static final String OEM = "oem_number";
}
