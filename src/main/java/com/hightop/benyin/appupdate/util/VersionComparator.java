package com.hightop.benyin.appupdate.util;

import com.hightop.benyin.appupdate.infrastructure.entity.AppVersion;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 版本比较工具类
 * 支持版本名称+版本号的组合比较
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Slf4j
public class VersionComparator {
    
    // 版本名称解析正则：支持更灵活的格式
    // 支持格式：x.y.z, x.y, x.y.z-suffix, x.y-suffix 等
    private static final Pattern VERSION_PATTERN = Pattern.compile("^(\\d+)\\.(\\d+)(?:\\.(\\d+))?(?:-([a-zA-Z]+)(\\d*))?$");
    
    /**
     * 判断是否需要更新
     * @param currentVersionCode 当前版本号
     * @param currentVersionName 当前版本名称
     * @param targetVersion 目标版本
     * @return 是否需要更新
     */
    public static boolean shouldUpdate(Integer currentVersionCode, String currentVersionName, AppVersion targetVersion) {
        // 管理员强制更新标志优先级最高
        if (targetVersion.getAdminForce()) {
            log.info("管理员强制更新，当前版本: {}-{}, 目标版本: {}-{}", 
                    currentVersionName, currentVersionCode, 
                    targetVersion.getVersionName(), targetVersion.getVersionCode());
            return true;
        }
        
        // 版本名称+版本号组合比较
        return compareVersions(currentVersionCode, currentVersionName, 
                              targetVersion.getVersionCode(), targetVersion.getVersionName());
    }
    
    /**
     * 版本组合比较
     * @param currentVersionCode 当前版本号
     * @param currentVersionName 当前版本名称
     * @param targetVersionCode 目标版本号
     * @param targetVersionName 目标版本名称
     * @return 是否需要更新
     */
    public static boolean compareVersions(Integer currentVersionCode, String currentVersionName,
                                        Integer targetVersionCode, String targetVersionName) {

        log.debug("版本比较 - 当前: {}-{}, 目标: {}-{}",
                currentVersionName, currentVersionCode, targetVersionName, targetVersionCode);

        // 1. 智能版本比较：优先使用版本名称，但考虑版本号
        if (StringUtils.hasText(currentVersionName) && StringUtils.hasText(targetVersionName)) {
            int nameComparison = compareVersionNamesIntelligent(currentVersionName, targetVersionName,
                                                               currentVersionCode, targetVersionCode);
            if (nameComparison != 0) {
                boolean needUpdate = nameComparison < 0;
                log.info("智能版本比较结果: {}-{} vs {}-{} = {}, 需要更新: {}",
                        currentVersionName, currentVersionCode, targetVersionName, targetVersionCode,
                        nameComparison, needUpdate);
                return needUpdate;
            }
        }

        // 2. 版本名称相同或无法比较时，使用版本号比较
        boolean needUpdate = currentVersionCode < targetVersionCode;
        log.info("版本号比较结果: {} vs {} = {}, 需要更新: {}",
                currentVersionCode, targetVersionCode,
                currentVersionCode.compareTo(targetVersionCode), needUpdate);
        return needUpdate;
    }
    
    /**
     * 智能版本名称比较（结合版本号）
     * @param version1 版本1名称
     * @param version2 版本2名称
     * @param versionCode1 版本1号
     * @param versionCode2 版本2号
     * @return 比较结果：-1(version1 < version2), 0(相等), 1(version1 > version2)
     */
    public static int compareVersionNamesIntelligent(String version1, String version2,
                                                   Integer versionCode1, Integer versionCode2) {
        if (version1 == null && version2 == null) return 0;
        if (version1 == null) return -1;
        if (version2 == null) return 1;

        VersionInfo v1 = parseVersion(version1);
        VersionInfo v2 = parseVersion(version2);

        // 如果都能解析成功，使用版本名称比较
        if (v1 != null && v2 != null) {
            return v1.compareTo(v2);
        }

        // 如果版本名称解析失败，但版本号差异很大，优先使用版本号
        if (Math.abs(versionCode1 - versionCode2) > 1) {
            log.debug("版本名称解析失败，版本号差异较大，使用版本号比较: {} vs {}", versionCode1, versionCode2);
            return versionCode1.compareTo(versionCode2);
        }

        // 版本号相近时，使用字符串比较
        log.debug("版本名称解析失败，版本号相近，使用字符串比较: {} vs {}", version1, version2);
        return version1.compareTo(version2);
    }

    /**
     * 版本名称比较
     * @param version1 版本1
     * @param version2 版本2
     * @return 比较结果：-1(version1 < version2), 0(相等), 1(version1 > version2)
     */
    public static int compareVersionNames(String version1, String version2) {
        if (version1 == null && version2 == null) return 0;
        if (version1 == null) return -1;
        if (version2 == null) return 1;

        VersionInfo v1 = parseVersion(version1);
        VersionInfo v2 = parseVersion(version2);

        // 如果解析失败，使用字符串比较
        if (v1 == null || v2 == null) {
            return version1.compareTo(version2);
        }

        return v1.compareTo(v2);
    }
    
    /**
     * 解析版本名称
     * @param versionName 版本名称
     * @return 版本信息对象
     */
    private static VersionInfo parseVersion(String versionName) {
        if (!StringUtils.hasText(versionName)) {
            return null;
        }
        
        Matcher matcher = VERSION_PATTERN.matcher(versionName.trim());
        if (!matcher.matches()) {
            log.warn("版本名称格式不匹配: {}", versionName);
            return null;
        }
        
        try {
            int major = Integer.parseInt(matcher.group(1));
            int minor = Integer.parseInt(matcher.group(2));
            // patch版本号可能为空（如1.0格式）
            int patch = matcher.group(3) != null ? Integer.parseInt(matcher.group(3)) : 0;
            String preRelease = matcher.group(4);  // 后缀，如debug、alpha、beta、rc等
            String preReleaseNumber = matcher.group(5);  // 预发布版本号

            return new VersionInfo(major, minor, patch, preRelease, preReleaseNumber);
        } catch (NumberFormatException e) {
            log.error("解析版本号失败: {}", versionName, e);
            return null;
        }
    }
    
    /**
     * 版本信息内部类
     */
    private static class VersionInfo implements Comparable<VersionInfo> {
        private final int major;
        private final int minor;
        private final int patch;
        private final String preRelease;
        private final int preReleaseNumber;
        
        public VersionInfo(int major, int minor, int patch, String preRelease, String preReleaseNumber) {
            this.major = major;
            this.minor = minor;
            this.patch = patch;
            this.preRelease = preRelease;
            this.preReleaseNumber = StringUtils.hasText(preReleaseNumber) ? 
                    Integer.parseInt(preReleaseNumber) : 0;
        }
        
        @Override
        public int compareTo(VersionInfo other) {
            // 比较主版本号
            int result = Integer.compare(this.major, other.major);
            if (result != 0) return result;
            
            // 比较次版本号
            result = Integer.compare(this.minor, other.minor);
            if (result != 0) return result;
            
            // 比较补丁版本号
            result = Integer.compare(this.patch, other.patch);
            if (result != 0) return result;
            
            // 比较预发布版本
            return comparePreRelease(this.preRelease, this.preReleaseNumber, 
                                   other.preRelease, other.preReleaseNumber);
        }
        
        private int comparePreRelease(String pre1, int num1, String pre2, int num2) {
            // 正式版本 > 预发布版本
            if (pre1 == null && pre2 != null) return 1;
            if (pre1 != null && pre2 == null) return -1;
            if (pre1 == null && pre2 == null) return 0;
            
            // 预发布版本优先级：alpha < beta < rc
            int priority1 = getPreReleasePriority(pre1);
            int priority2 = getPreReleasePriority(pre2);
            
            int result = Integer.compare(priority1, priority2);
            if (result != 0) return result;
            
            // 同类型预发布版本比较版本号
            return Integer.compare(num1, num2);
        }
        
        private int getPreReleasePriority(String preRelease) {
            switch (preRelease.toLowerCase()) {
                case "alpha": return 1;
                case "beta": return 2;
                case "rc": return 3;
                case "debug": return 0;  // debug版本优先级最低
                case "snapshot": return 0;  // snapshot版本优先级最低
                case "dev": return 0;  // dev版本优先级最低
                default: return 2;  // 未知后缀默认为beta级别
            }
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append(major).append(".").append(minor).append(".").append(patch);
            if (preRelease != null) {
                sb.append("-").append(preRelease);
                if (preReleaseNumber > 0) {
                    sb.append(preReleaseNumber);
                }
            }
            return sb.toString();
        }
    }
}
