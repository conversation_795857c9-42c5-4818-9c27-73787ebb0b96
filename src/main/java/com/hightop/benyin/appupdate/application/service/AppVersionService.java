package com.hightop.benyin.appupdate.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hightop.benyin.appupdate.api.dto.AppVersionPublishDto;
import com.hightop.benyin.appupdate.api.dto.query.AppVersionPageQuery;
import com.hightop.benyin.appupdate.api.vo.AppVersionVo;
import com.hightop.benyin.appupdate.domain.service.AppVersionDomainService;
import com.hightop.benyin.appupdate.domain.service.AppVersionDistributionDomainService;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersionDistribution;
import com.hightop.benyin.appupdate.api.dto.TargetedReleaseDto;
import com.hightop.benyin.appupdate.api.dto.DistributionDetailDto;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersion;
import com.hightop.benyin.share.application.service.CosService;
import com.hightop.benyin.share.application.vo.CosBucket;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.restful.tencent.CosProperties;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 应用版本管理服务
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Transactional
@Slf4j
public class AppVersionService {

    AppVersionDomainService appVersionDomainService;
    AppVersionDistributionDomainService distributionDomainService;
    CosService cosService;
    SequenceDomainService sequenceDomainService;
    CosProperties cosProperties;

    /**
     * 分页查询版本列表
     *
     * @param pageQuery 查询条件
     * @return 分页结果
     */
    public DataGrid<AppVersionVo> page(AppVersionPageQuery pageQuery) {
        LambdaQueryWrapper<AppVersion> wrapper = Wrappers.<AppVersion>lambdaQuery()
                .like(StringUtils.isNotEmpty(pageQuery.getVersionName()),
                        AppVersion::getVersionName, pageQuery.getVersionName())
                .eq(pageQuery.getIsActive() != null,
                        AppVersion::getIsActive, pageQuery.getIsActive())
                .eq(pageQuery.getIsForce() != null,
                        AppVersion::getIsForce, pageQuery.getIsForce())
                .orderByDesc(AppVersion::getCreatedAt);

        return PageHelper.startPage(pageQuery, p -> {
            List<AppVersion> list = appVersionDomainService.list(wrapper);
            return list.stream().map(this::convertToVo).collect(Collectors.toList());
        });
    }

    /**
     * 发布新版本
     *
     * @param publishDto 发布信息
     * @return 版本ID
     */
    public Long publishVersion(AppVersionPublishDto publishDto) {
        // 验证版本号唯一性
        validateVersionUnique(publishDto.getVersionName(), publishDto.getVersionCode(), null);

        // 创建版本记录（使用前端已上传的文件信息）
        AppVersion version = new AppVersion()
                .setVersionName(publishDto.getVersionName())
                .setVersionCode(publishDto.getVersionCode())
                .setApkFileName(publishDto.getApkFileName())
                .setCosKey(publishDto.getCosKey())
                .setCosUrl(publishDto.getCosUrl())
                .setFileSize(publishDto.getFileSize())
                .setFileMd5(publishDto.getFileMd5())
                .setUpdateLog(publishDto.getUpdateLog())
                .setIsForce(publishDto.getIsForce())
                .setAdminForce(false)
                .setIsActive(publishDto.getIsActive())
                .setDownloadCount(0)
                .setCreatedBy(ApplicationSessions.id());

        appVersionDomainService.save(version);

        return version.getId();
    }

    /**
     * 更新版本信息
     *
     * @param id          版本ID
     * @param versionName 版本名称
     * @param updateLog   更新说明
     * @param isForce     是否强制更新
     * @param isActive    是否启用
     * @return 是否成功
     */
    public boolean updateVersion(Long id, String versionName, String updateLog,
                                 Boolean isForce, Boolean isActive) {
        AppVersion version = appVersionDomainService.getById(id);
        if (version == null) {
            throw new MaginaException("版本不存在");
        }

        // 如果修改了版本名称，需要验证唯一性
        if (!version.getVersionName().equals(versionName)) {
            validateVersionUnique(versionName, null, id);
        }

        return appVersionDomainService.lambdaUpdate()
                .set(AppVersion::getVersionName, versionName)
                .set(AppVersion::getUpdateLog, updateLog)
                .set(AppVersion::getIsForce, isForce)
                .set(AppVersion::getIsActive, isActive)
                .set(AppVersion::getUpdatedBy, ApplicationSessions.id())
                .eq(AppVersion::getId, id)
                .update();
    }

    /**
     * 设置/取消强制更新
     *
     * @param id    版本ID
     * @param force 是否强制
     * @return 是否成功
     */
    public boolean toggleForce(Long id, Boolean force) {
        return appVersionDomainService.setForceUpdate(id, force);
    }

    /**
     * 删除版本
     *
     * @param id 版本ID
     * @return 是否成功
     */
    public boolean deleteVersion(Long id) {
        return appVersionDomainService.removeById(id);
    }

    /**
     * 紧急操作处理
     *
     * @param action 操作类型
     * @param params 参数
     * @return 是否成功
     */
    public boolean emergencyAction(String action, Map<String, Object> params) {
        switch (action) {
            case "rollback":
                return handleRollback(params);
            case "pause":
                return handlePauseUpdates();
            case "resume":
                return handleResumeUpdates();
            default:
                throw new MaginaException("不支持的紧急操作: " + action);
        }
    }

    /**
     * 处理版本回退
     *
     * @param params 参数
     * @return 是否成功
     */
    private boolean handleRollback(Map<String, Object> params) {
        String targetVersion = (String) params.get("targetVersion");
        if (StringUtils.isEmpty(targetVersion)) {
            throw new MaginaException("目标版本不能为空");
        }

        AppVersion targetVersionEntity = appVersionDomainService.getByVersionName(targetVersion);
        if (targetVersionEntity == null) {
            throw new MaginaException("目标版本不存在");
        }

        // 设置管理员强制更新标志
        return appVersionDomainService.lambdaUpdate()
                .set(AppVersion::getAdminForce, true)
                .eq(AppVersion::getId, targetVersionEntity.getId())
                .update();
    }

    /**
     * 暂停所有更新
     *
     * @return 是否成功
     */
    private boolean handlePauseUpdates() {
        return appVersionDomainService.pauseAllUpdates();
    }

    /**
     * 恢复更新推送
     *
     * @return 是否成功
     */
    private boolean handleResumeUpdates() {
        return appVersionDomainService.resumeAllUpdates();
    }

    /**
     * 验证版本唯一性（修正版，与数据库约束一致）
     * 校验 version_name + version_code + deleted 的组合唯一性
     *
     * @param versionName 版本名称
     * @param versionCode 版本号
     * @param excludeId   排除的ID（更新时使用）
     */
    private synchronized void validateVersionUnique(String versionName, Integer versionCode, Long excludeId) {
        // 使用 synchronized 确保并发安全
        if (StringUtils.isNotEmpty(versionName) && versionCode != null) {
            // 校验 version_name + version_code 的组合唯一性（只考虑未删除的记录）
            boolean exists = appVersionDomainService.existsByVersionNameAndCode(versionName, versionCode, excludeId);
            if (exists) {
                throw new MaginaException("版本 '" + versionName + "' (版本号: " + versionCode + ") 已存在，请使用其他版本名称或版本号");
            }
        }
    }


    /**
     * 转换为VO
     *
     * @param entity 实体
     * @return VO
     */
    private AppVersionVo convertToVo(AppVersion entity) {
        AppVersionVo vo = new AppVersionVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    /**
     * 设置定向发布
     *
     * @param versionId  版本ID
     * @param releaseDto 发布信息
     */
    @Transactional
    public void setTargetedRelease(Long versionId, TargetedReleaseDto releaseDto) {
        // 1. 更新版本发布类型
        appVersionDomainService.lambdaUpdate()
                .set(AppVersion::getReleaseType, "TARGETED")
                .eq(AppVersion::getId, versionId)
                .update();

        // 2. 清除指定用户/设备的所有现有定向版本关系（覆盖策略）
        clearExistingDistributions(releaseDto);

        // 3. 创建新的分发关系
        List<AppVersionDistribution> distributions = new ArrayList<>();

        // 用户分发（优先使用详细信息，兼容旧版本）
        if (releaseDto.getUsers() != null && !releaseDto.getUsers().isEmpty()) {
            // 使用新版本的详细用户信息
            for (TargetedReleaseDto.TargetUserInfo user : releaseDto.getUsers()) {
                distributions.add(createDistribution(versionId, "USER", user.getId(), user.getName()));
            }
        } else if (releaseDto.getUserIds() != null) {
            // 兼容旧版本的用户ID列表
            for (String userId : releaseDto.getUserIds()) {
                distributions.add(createDistribution(versionId, "USER", userId, null));
            }
        }

        // 设备分发（优先使用详细信息，兼容旧版本）
        if (releaseDto.getDevices() != null && !releaseDto.getDevices().isEmpty()) {
            // 使用新版本的详细设备信息
            for (TargetedReleaseDto.TargetDeviceInfo device : releaseDto.getDevices()) {
                distributions.add(createDistribution(versionId, "DEVICE", device.getId(), device.getName()));
            }
        } else if (releaseDto.getDeviceIds() != null) {
            // 兼容旧版本的设备ID列表
            for (String deviceId : releaseDto.getDeviceIds()) {
                distributions.add(createDistribution(versionId, "DEVICE", deviceId, null));
            }
        }

        // 用户组分发（优先使用详细信息，兼容旧版本）
        if (releaseDto.getGroups() != null && !releaseDto.getGroups().isEmpty()) {
            // 使用新版本的详细用户组信息
            for (TargetedReleaseDto.TargetGroupInfo group : releaseDto.getGroups()) {
                distributions.add(createDistribution(versionId, "GROUP", group.getId(), group.getName()));
            }
        } else if (releaseDto.getGroupIds() != null) {
            // 兼容旧版本的用户组ID列表
            for (String groupId : releaseDto.getGroupIds()) {
                distributions.add(createDistribution(versionId, "GROUP", groupId, null));
            }
        }

        // 批量保存
        if (!distributions.isEmpty()) {
            distributionDomainService.batchSave(distributions);
        }
    }

    /**
     * 设置全局发布
     *
     * @param versionId 版本ID
     */
    @Transactional
    public void setGlobalRelease(Long versionId) {
        // 1. 更新版本发布类型
        appVersionDomainService.lambdaUpdate()
                .set(AppVersion::getReleaseType, "GLOBAL")
                .eq(AppVersion::getId, versionId)
                .update();

        // 2. 删除所有分发关系
        distributionDomainService.deleteByVersionId(versionId);
    }

    /**
     * 获取版本分发情况
     *
     * @param versionId 版本ID
     * @return 分发关系列表
     */
    public List<AppVersionDistribution> getVersionDistributions(Long versionId) {
        return distributionDomainService.getDistributionsByVersion(versionId);
    }

    /**
     * 获取所有正常的分发关系配置列表
     * @param isActiveOnly 是否只返回激活状态的分发关系
     * @param includeDeleted 是否包含已删除的记录
     * @return 分发关系列表
     */
    public List<AppVersionDistribution> getAllDistributions(Boolean isActiveOnly, Boolean includeDeleted) {
        LambdaQueryWrapper<AppVersionDistribution> wrapper = new LambdaQueryWrapper<>();

        // 是否只返回激活状态
        if (isActiveOnly != null && isActiveOnly) {
            wrapper.eq(AppVersionDistribution::getIsActive, true);
        }

        // 是否包含已删除的记录
        if (includeDeleted == null || !includeDeleted) {
            wrapper.eq(AppVersionDistribution::getDeleted, 0);
        }

        wrapper.orderByDesc(AppVersionDistribution::getCreatedAt);

        return distributionDomainService.list(wrapper);
    }

    /**
     * 获取所有正常的分发关系配置列表（兼容旧方法）
     * @param isActiveOnly 是否只返回激活状态的分发关系
     * @return 分发关系列表
     */
    public List<AppVersionDistribution> getAllDistributions(Boolean isActiveOnly) {
        return getAllDistributions(isActiveOnly, false);
    }

    /**
     * 获取所有分发关系的详细信息（包含版本信息）
     * @param isActiveOnly 是否只返回激活状态的分发关系
     * @param includeDeleted 是否包含已删除的记录
     * @return 分发关系详细信息列表
     */
    public List<DistributionDetailDto> getAllDistributionsWithDetail(Boolean isActiveOnly, Boolean includeDeleted) {
        // 获取分发关系
        List<AppVersionDistribution> distributions = getAllDistributions(isActiveOnly, includeDeleted);

        // 转换为详细信息DTO
        return distributions.stream().map(distribution -> {
            DistributionDetailDto dto = new DistributionDetailDto();
            dto.setId(distribution.getId());
            dto.setVersionId(distribution.getVersionId());
            dto.setTargetType(distribution.getTargetType());
            dto.setTargetId(distribution.getTargetId());
            dto.setTargetName(distribution.getTargetName());
            dto.setIsActive(distribution.getIsActive());
            dto.setAssignTime(distribution.getAssignTime());
            dto.setCreatedAt(distribution.getCreatedAt());
            dto.setCreatedBy(distribution.getCreatedBy());

            // 获取版本信息
            AppVersion version = appVersionDomainService.getById(distribution.getVersionId());
            if (version != null) {
                dto.setVersionName(version.getVersionName());
                dto.setVersionCode(version.getVersionCode());
                dto.setReleaseType(version.getReleaseType());
            }

            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 获取所有分发关系的详细信息（兼容旧方法）
     * @param isActiveOnly 是否只返回激活状态的分发关系
     * @return 分发关系详细信息列表
     */
    public List<DistributionDetailDto> getAllDistributionsWithDetail(Boolean isActiveOnly) {
        return getAllDistributionsWithDetail(isActiveOnly, false);
    }

    /**
     * 移除单个用户/设备/用户组的分发关系
     * @param versionId 版本ID
     * @param targetType 目标类型：USER-用户，DEVICE-设备，GROUP-用户组
     * @param targetId 目标ID
     * @return 是否成功
     */
    @Transactional
    public boolean removeTargetDistribution(Long versionId, String targetType, String targetId) {
        return distributionDomainService.lambdaUpdate()
                .set(AppVersionDistribution::getDeleted, 1)
                .eq(AppVersionDistribution::getVersionId, versionId)
                .eq(AppVersionDistribution::getTargetType, targetType)
                .eq(AppVersionDistribution::getTargetId, targetId)
                .eq(AppVersionDistribution::getIsActive, true)
                .update();
    }

    /**
     * 取消激活单个用户/设备/用户组的分发关系（不删除，只是设为不激活）
     * @param versionId 版本ID
     * @param targetType 目标类型：USER-用户，DEVICE-设备，GROUP-用户组
     * @param targetId 目标ID
     * @return 是否成功
     */
    @Transactional
    public boolean deactivateTargetDistribution(Long versionId, String targetType, String targetId) {
        return distributionDomainService.lambdaUpdate()
                .set(AppVersionDistribution::getIsActive, false)
                .eq(AppVersionDistribution::getVersionId, versionId)
                .eq(AppVersionDistribution::getTargetType, targetType)
                .eq(AppVersionDistribution::getTargetId, targetId)
                .eq(AppVersionDistribution::getIsActive, true)
                .update();
    }

    /**
     * 重新激活单个用户/设备/用户组的分发关系
     * @param versionId 版本ID
     * @param targetType 目标类型：USER-用户，DEVICE-设备，GROUP-用户组
     * @param targetId 目标ID
     * @return 是否成功
     */
    @Transactional
    public boolean activateTargetDistribution(Long versionId, String targetType, String targetId) {
        return distributionDomainService.lambdaUpdate()
                .set(AppVersionDistribution::getIsActive, true)
                .eq(AppVersionDistribution::getVersionId, versionId)
                .eq(AppVersionDistribution::getTargetType, targetType)
                .eq(AppVersionDistribution::getTargetId, targetId)
                .eq(AppVersionDistribution::getDeleted, 0)
                .update();
    }

    /**
     * 清除指定用户/设备的现有定向版本关系
     *
     * @param releaseDto 发布信息
     */
    private void clearExistingDistributions(TargetedReleaseDto releaseDto) {
        // 清除指定用户的现有定向版本（优先使用详细信息，兼容旧版本）
        if (releaseDto.getUsers() != null && !releaseDto.getUsers().isEmpty()) {
            for (TargetedReleaseDto.TargetUserInfo user : releaseDto.getUsers()) {
                distributionDomainService.lambdaUpdate()
                        .set(AppVersionDistribution::getDeleted, 1)
                        .eq(AppVersionDistribution::getTargetType, "USER")
                        .eq(AppVersionDistribution::getTargetId, user.getId())
                        .eq(AppVersionDistribution::getIsActive, true)
                        .update();
            }
        } else if (releaseDto.getUserIds() != null) {
            for (String userId : releaseDto.getUserIds()) {
                distributionDomainService.lambdaUpdate()
                        .set(AppVersionDistribution::getDeleted, 1)
                        .eq(AppVersionDistribution::getTargetType, "USER")
                        .eq(AppVersionDistribution::getTargetId, userId)
                        .eq(AppVersionDistribution::getIsActive, true)
                        .update();
            }
        }

        // 清除指定设备的现有定向版本（优先使用详细信息，兼容旧版本）
        if (releaseDto.getDevices() != null && !releaseDto.getDevices().isEmpty()) {
            for (TargetedReleaseDto.TargetDeviceInfo device : releaseDto.getDevices()) {
                distributionDomainService.lambdaUpdate()
                        .set(AppVersionDistribution::getDeleted, 1)
                        .eq(AppVersionDistribution::getTargetType, "DEVICE")
                        .eq(AppVersionDistribution::getTargetId, device.getId())
                        .eq(AppVersionDistribution::getIsActive, true)
                        .update();
            }
        } else if (releaseDto.getDeviceIds() != null) {
            for (String deviceId : releaseDto.getDeviceIds()) {
                distributionDomainService.lambdaUpdate()
                        .set(AppVersionDistribution::getDeleted, 1)
                        .eq(AppVersionDistribution::getTargetType, "DEVICE")
                        .eq(AppVersionDistribution::getTargetId, deviceId)
                        .eq(AppVersionDistribution::getIsActive, true)
                        .update();
            }
        }

        // 清除指定用户组的现有定向版本（优先使用详细信息，兼容旧版本）
        if (releaseDto.getGroups() != null && !releaseDto.getGroups().isEmpty()) {
            for (TargetedReleaseDto.TargetGroupInfo group : releaseDto.getGroups()) {
                distributionDomainService.lambdaUpdate()
                        .set(AppVersionDistribution::getDeleted, 1)
                        .eq(AppVersionDistribution::getTargetType, "GROUP")
                        .eq(AppVersionDistribution::getTargetId, group.getId())
                        .eq(AppVersionDistribution::getIsActive, true)
                        .update();
            }
        } else if (releaseDto.getGroupIds() != null) {
            for (String groupId : releaseDto.getGroupIds()) {
                distributionDomainService.lambdaUpdate()
                        .set(AppVersionDistribution::getDeleted, 1)
                        .eq(AppVersionDistribution::getTargetType, "GROUP")
                        .eq(AppVersionDistribution::getTargetId, groupId)
                        .eq(AppVersionDistribution::getIsActive, true)
                        .update();
            }
        }
    }

    /**
     * 创建分发关系
     */
    private AppVersionDistribution createDistribution(Long versionId, String targetType,
                                                      String targetId, String targetName) {
        return new AppVersionDistribution()
                .setVersionId(versionId)
                .setTargetType(targetType)
                .setTargetId(targetId)
                .setTargetName(targetName)
                .setIsActive(true)
                .setCreatedBy(ApplicationSessions.id().toString());
    }
}
