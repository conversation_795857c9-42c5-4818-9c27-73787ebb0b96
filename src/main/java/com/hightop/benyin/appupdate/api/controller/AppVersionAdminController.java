package com.hightop.benyin.appupdate.api.controller;

import com.hightop.benyin.appupdate.api.dto.AppVersionPublishDto;
import com.hightop.benyin.appupdate.api.dto.query.AppVersionPageQuery;
import com.hightop.benyin.appupdate.api.vo.AppVersionVo;
import com.hightop.benyin.appupdate.application.service.AppVersionService;
import com.hightop.benyin.appupdate.api.dto.TargetedReleaseDto;
import com.hightop.benyin.appupdate.api.dto.DistributionDetailDto;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersionDistribution;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 应用版本管理控制器
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController
@RequestMapping("/admin/app-version")
@Api(tags = "应用版本管理")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppVersionAdminController {
    
    AppVersionService appVersionService;
    
    /**
     * 版本列表分页查询
     * @param pageQuery 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation("版本列表分页查询")
    public RestResponse<DataGrid<AppVersionVo>> page(@RequestBody AppVersionPageQuery pageQuery) {
        return RestResponse.ok(appVersionService.page(pageQuery));
    }
    
    /**
     * 发布新版本
     * @param publishDto 发布信息
     * @return 版本ID
     */
    @PostMapping("/publish")
    @ApiOperation("发布新版本")
    public RestResponse<Long> publish(@Validated @RequestBody AppVersionPublishDto publishDto) {
        return RestResponse.ok(appVersionService.publishVersion(publishDto));
    }
    
    /**
     * 更新版本信息
     * @param id 版本ID
     * @param versionName 版本名称
     * @param updateLog 更新说明
     * @param isForce 是否强制更新
     * @param isActive 是否启用
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @ApiOperation("更新版本信息")
    public RestResponse<Void> update(@PathVariable Long id,
                                   @RequestParam String versionName,
                                   @RequestParam(required = false) String updateLog,
                                   @RequestParam(required = false) Boolean isForce,
                                   @RequestParam(required = false) Boolean isActive) {
        return Operation.UPDATE.response(
            appVersionService.updateVersion(id, versionName, updateLog, isForce, isActive));
    }
    
    /**
     * 设置/取消强制更新
     * @param id 版本ID
     * @param force 是否强制
     * @return 操作结果
     */
    @PostMapping("/{id}/force")
    @ApiOperation("设置/取消强制更新")
    public RestResponse<Void> toggleForce(@PathVariable Long id, 
                                        @RequestParam Boolean force) {
        return Operation.UPDATE.response(appVersionService.toggleForce(id, force));
    }
    
    /**
     * 删除版本
     * @param id 版本ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除版本")
    public RestResponse<Void> delete(@PathVariable Long id) {
        return Operation.DELETE.response(appVersionService.deleteVersion(id));
    }
    
    /**
     * 紧急操作
     * @param action 操作类型
     * @param params 参数
     * @return 操作结果
     */
    @PostMapping("/emergency/{action}")
    @ApiOperation("紧急操作")
    public RestResponse<Void> emergency(@PathVariable String action,
                                      @RequestBody(required = false) Map<String, Object> params) {
        return Operation.UPDATE.response(appVersionService.emergencyAction(action, params));
    }

    /**
     * 设置版本定向发布
     * @param versionId 版本ID
     * @param releaseDto 定向发布信息
     * @return 操作结果
     */
    @PostMapping("/{versionId}/targeted-release")
    @ApiOperation("设置版本定向发布")
    public RestResponse<Void> setTargetedRelease(@PathVariable Long versionId,
                                               @Valid @RequestBody TargetedReleaseDto releaseDto) {
        appVersionService.setTargetedRelease(versionId, releaseDto);
        return RestResponse.ok(null);
    }

    /**
     * 将定向发布版本转为全局发布
     * @param versionId 版本ID
     * @return 操作结果
     */
    @PutMapping("/{versionId}/global-release")
    @ApiOperation("转为全局发布")
    public RestResponse<Void> setGlobalRelease(@PathVariable Long versionId) {
        appVersionService.setGlobalRelease(versionId);
        return RestResponse.ok(null);
    }

    /**
     * 查看版本分发情况
     * @param versionId 版本ID
     * @return 分发关系列表
     */
    @GetMapping("/{versionId}/distributions")
    @ApiOperation("查看版本分发情况")
    public RestResponse<List<AppVersionDistribution>> getVersionDistributions(@PathVariable Long versionId) {
        return RestResponse.ok(appVersionService.getVersionDistributions(versionId));
    }

    /**
     * 获取所有分发关系配置列表
     * @param isActiveOnly 是否只返回激活状态的分发关系，默认false
     * @param includeDeleted 是否包含已删除的记录，默认false
     * @return 分发关系列表
     */
    @GetMapping("/distributions")
    @ApiOperation("获取所有分发关系配置列表")
    public RestResponse<List<AppVersionDistribution>> getAllDistributions(
            @RequestParam(required = false, defaultValue = "false") Boolean isActiveOnly,
            @RequestParam(required = false, defaultValue = "false") Boolean includeDeleted) {
        return RestResponse.ok(appVersionService.getAllDistributions(isActiveOnly, includeDeleted));
    }

    /**
     * 获取所有分发关系的详细信息（包含版本信息）
     * @param isActiveOnly 是否只返回激活状态的分发关系，默认false
     * @param includeDeleted 是否包含已删除的记录，默认false
     * @return 分发关系详细信息列表
     */
    @GetMapping("/distributions/detail")
    @ApiOperation("获取所有分发关系详细信息")
    public RestResponse<List<DistributionDetailDto>> getAllDistributionsWithDetail(
            @RequestParam(required = false, defaultValue = "false") Boolean isActiveOnly,
            @RequestParam(required = false, defaultValue = "false") Boolean includeDeleted) {
        return RestResponse.ok(appVersionService.getAllDistributionsWithDetail(isActiveOnly, includeDeleted));
    }

    /**
     * 移除单个用户/设备/用户组的分发关系
     * @param versionId 版本ID
     * @param targetType 目标类型：USER-用户，DEVICE-设备，GROUP-用户组
     * @param targetId 目标ID
     * @return 操作结果
     */
    @DeleteMapping("/{versionId}/distributions/{targetType}/{targetId}")
    @ApiOperation("移除单个分发关系")
    public RestResponse<Void> removeTargetDistribution(@PathVariable Long versionId,
                                                      @PathVariable String targetType,
                                                      @PathVariable String targetId) {
        boolean success = appVersionService.removeTargetDistribution(versionId, targetType, targetId);
        if (success) {
            return RestResponse.ok(null);
        } else {
            // 根据项目规范，错误情况也返回ok(null)，错误信息记录在日志中
            return new RestResponse<>(400, "移除分发关系失败", null, null);
        }
    }

    /**
     * 取消激活单个用户/设备/用户组的分发关系
     * @param versionId 版本ID
     * @param targetType 目标类型：USER-用户，DEVICE-设备，GROUP-用户组
     * @param targetId 目标ID
     * @return 操作结果
     */
    @PutMapping("/{versionId}/distributions/{targetType}/{targetId}/deactivate")
    @ApiOperation("取消激活分发关系")
    public RestResponse<Void> deactivateTargetDistribution(@PathVariable Long versionId,
                                                          @PathVariable String targetType,
                                                          @PathVariable String targetId) {
        boolean success = appVersionService.deactivateTargetDistribution(versionId, targetType, targetId);
        if (success) {
            return RestResponse.ok(null);
        } else {
            // 根据项目规范，错误情况也返回ok(null)，错误信息记录在日志中
            return new RestResponse<>(400, "取消激活分发关系失败", null, null);
        }
    }

    /**
     * 重新激活单个用户/设备/用户组的分发关系
     * @param versionId 版本ID
     * @param targetType 目标类型：USER-用户，DEVICE-设备，GROUP-用户组
     * @param targetId 目标ID
     * @return 操作结果
     */
    @PutMapping("/{versionId}/distributions/{targetType}/{targetId}/activate")
    @ApiOperation("重新激活分发关系")
    public RestResponse<Void> activateTargetDistribution(@PathVariable Long versionId,
                                                        @PathVariable String targetType,
                                                        @PathVariable String targetId) {
        boolean success = appVersionService.activateTargetDistribution(versionId, targetType, targetId);
        if (success) {
            return RestResponse.ok(null);
        } else {
            // 根据项目规范，错误情况也返回ok(null)，错误信息记录在日志中
            return new RestResponse<>(400, "激活分发关系失败", null, null);
        }
    }
}
