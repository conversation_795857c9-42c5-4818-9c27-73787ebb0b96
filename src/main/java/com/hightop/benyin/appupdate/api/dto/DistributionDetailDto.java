package com.hightop.benyin.appupdate.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * 分发关系详细信息DTO
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("分发关系详细信息")
public class DistributionDetailDto {
    
    @ApiModelProperty("分发关系ID")
    Long id;
    
    @ApiModelProperty("版本ID")
    Long versionId;
    
    @ApiModelProperty("版本名称")
    String versionName;
    
    @ApiModelProperty("版本号")
    Integer versionCode;
    
    @ApiModelProperty("发布类型")
    String releaseType;
    
    @ApiModelProperty("目标类型：USER-用户，DEVICE-设备，GROUP-用户组")
    String targetType;
    
    @ApiModelProperty("目标ID")
    String targetId;
    
    @ApiModelProperty("目标名称")
    String targetName;
    
    @ApiModelProperty("是否激活")
    Boolean isActive;
    
    @ApiModelProperty("分配时间")
    LocalDateTime assignTime;
    
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;
    
    @ApiModelProperty("创建人")
    String createdBy;
}
