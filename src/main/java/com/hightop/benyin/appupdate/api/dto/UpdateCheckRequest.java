package com.hightop.benyin.appupdate.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

/**
 * 更新检查请求DTO
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@ApiModel("更新检查请求")
public class UpdateCheckRequest {
    
    @ApiModelProperty("当前版本号")
    @NotNull(message = "当前版本号不能为空")
    Integer currentVersionCode;

    @ApiModelProperty("当前版本名称")
    String currentVersionName;

    @ApiModelProperty("设备ID")
    String deviceId;

    @ApiModelProperty("用户ID（支持定向更新）")
    String userId;

    @ApiModelProperty("IP地址")
    String ipAddress;
    
    @ApiModelProperty("用户代理")
    String userAgent;
}
