package com.hightop.benyin.appupdate.api.controller;

import com.hightop.benyin.appupdate.api.dto.UpdateCheckRequest;
import com.hightop.benyin.appupdate.api.dto.UpdateCheckResponse;
import com.hightop.benyin.appupdate.application.service.AppUpdateService;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.core.annotation.Anonymous;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 应用更新控制器
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController
@RequestMapping("/app")
@Api(tags = "应用更新")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppUpdateController {
    
    AppUpdateService appUpdateService;
    
    /**
     * 检查应用更新
     * @param currentVersionCode 当前版本号
     * @param currentVersionName 当前版本名称（可选，用于精确版本比较）
     * @param deviceId 设备ID
     * @param userId 用户ID（支持定向更新）
     * @param request HTTP请求
     * @return 更新检查结果
     */
    @GetMapping("/update")
    @ApiOperation("检查应用更新")
    @Anonymous
    @IgnoreOperationLog
    public RestResponse<UpdateCheckResponse> checkUpdate(
            @RequestParam @ApiParam("当前版本号") Integer currentVersionCode,
            @RequestParam(required = false) @ApiParam("当前版本名称") String currentVersionName,
            @RequestParam(required = false) @ApiParam("设备ID") String deviceId,
            @RequestParam(required = false) @ApiParam("用户ID") String userId,
            HttpServletRequest request) {

        UpdateCheckRequest checkRequest = new UpdateCheckRequest()
                .setCurrentVersionCode(currentVersionCode)
                .setCurrentVersionName(currentVersionName)
                .setDeviceId(deviceId)
                .setUserId(userId)
                .setIpAddress(getClientIpAddress(request))
                .setUserAgent(request.getHeader("User-Agent"));

        return RestResponse.ok(appUpdateService.checkUpdate(checkRequest));
    }
    
    /**
     * 下载APK文件
     * @param versionId 版本ID
     * @param response HTTP响应
     * @param request HTTP请求
     */
    @GetMapping("/download/{versionId}")
    @ApiOperation("下载APK文件")
    @Anonymous
    public void downloadApk(@PathVariable Long versionId, 
                           HttpServletResponse response,
                           HttpServletRequest request) {
        appUpdateService.downloadApk(versionId, request, response);
    }
    
    /**
     * 获取客户端IP地址
     * @param request HTTP请求
     * @return IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }
}
