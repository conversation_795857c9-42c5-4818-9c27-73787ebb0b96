# statistics/queryEngineerMonthTimelinessList 接口详细说明文档

## 接口概述

**接口路径**: `/statistics/queryEngineerMonthTimelinessList`  
**请求方式**: POST  
**接口作用**: 工程师月度时效-月度统计  
**接口描述**: 用于统计分析工程师在指定月份范围内的工作时效数据，包括接单时间、维修时间、评价等各项指标的统计汇总

## 控制器层实现

### 控制器位置
- **文件路径**: `src/main/java/com/hightop/benyin/statistics/api/controller/TStatisticsEngineerTimelinessController.java`
- **控制器类**: `TStatisticsEngineerTimelinessController`
- **方法**: `queryEngineerMonthTimelinessList(@RequestBody EngineerMonthTimelinessQuery query)`

### 控制器代码逻辑
```java
@PostMapping("/queryEngineerMonthTimelinessList")
@ApiOperation("工程师月度时效-月度统计")
@IgnoreOperationLog
@Anonymous
public RestResponse<DataGrid<TStatisticsEngineerTimelinessVO>> queryEngineerMonthTimelinessList(@RequestBody EngineerMonthTimelinessQuery query) {
    return RestResponse.ok(this.timelinessService.queryEngineerMonthTimelinessList(query));
}
```

## 服务层实现

### 服务类位置
- **文件路径**: `src/main/java/com/hightop/benyin/statistics/application/service/TStatisticsEngineerTimelinessService.java`
- **方法**: `queryEngineerMonthTimelinessList(EngineerMonthTimelinessQuery query)`

### 核心实现逻辑
服务层直接调用领域服务层的方法：
```java
public DataGrid<TStatisticsEngineerTimelinessVO> queryEngineerMonthTimelinessList(EngineerMonthTimelinessQuery query) {
    return timelinessDomainService.queryEngineerMonthTimelinessList(query);
}
```

## 领域服务层实现

### 领域服务类位置
- **文件路径**: `src/main/java/com/hightop/benyin/statistics/domain/service/TStatisticsEngineerTimelinessDomainService.java`
- **方法**: `queryEngineerMonthTimelinessList(EngineerMonthTimelinessQuery query)`

### 核心实现逻辑
```java
public DataGrid<TStatisticsEngineerTimelinessVO> queryEngineerMonthTimelinessList(EngineerMonthTimelinessQuery query) {
    PageHelper.startPage(query.getPageNumber(), query.getPageSize());
    return DataGrid.of(this.baseMapper.queryEngineerMonthTimelinessList(query));
}
```

## 数据访问层实现

### Mapper接口
- **文件路径**: `src/main/java/com/hightop/benyin/statistics/infrastructure/mapper/TStatisticsEngineerTimelinessMapper.java`
- **方法**: `queryEngineerMonthTimelinessList(@Param("query") EngineerMonthTimelinessQuery query)`

### SQL实现
- **文件路径**: `src/main/resources/mapper/TStatisticsEngineerTimelinessMapper.xml`

#### 核心SQL逻辑
```sql
SELECT
    monthly,                                                    -- 月份
    engineer_name,                                             -- 工程师名称
    engineer_id,                                               -- 工程师ID
    count( id ) orderNums,                                     -- 工单总数
    SUM( receive_num ) receive_num,                            -- 接单数量总和
    ROUND( AVG( IFNULL( receive_time, 0 )), 2 ) receive_time_avg,      -- 平均接单时间
    ROUND( AVG( IFNULL( prepare_time, 0 )), 2 ) prepare_time_avg,      -- 平均备料时间
    ROUND( AVG( IFNULL( on_road_time, 0 )), 2 ) on_road_time_avg,      -- 平均路上时间
    ROUND( AVG( IFNULL( repair_time, 0 )), 2 ) repair_time_avg,        -- 平均维修时间
    ROUND( AVG( IFNULL( confirm_time, 0 )), 2 ) confirm_time_avg,       -- 平均确认时间
    ROUND( AVG( IFNULL( professional_evaluate, 0 )), 2 ) professional_evaluate_avg,  -- 平均专业评分
    ROUND( AVG( IFNULL( service_evaluate, 0 )), 2 ) service_evaluate_avg,           -- 平均服务态度评分
    SUM( receive_time ) receive_time,                          -- 接单时间总和
    SUM( prepare_time ) prepare_time,                          -- 备料时间总和
    SUM( on_road_time ) on_road_time,                          -- 路上时间总和
    SUM( repair_time ) repair_time,                            -- 维修时间总和
    SUM( confirm_time ) confirm_time,                          -- 确认时间总和
    SUM( professional_evaluate ) professional_evaluate,        -- 专业评分总和
    SUM( service_evaluate ) service_evaluate                   -- 服务态度评分总和
FROM
    t_statistics_engineer_timeliness
WHERE
    -- 月份范围查询
    monthly >= #{query.startMonth}    (如果startMonth不为空)
    AND monthly <= #{query.endMonth}  (如果endMonth不为空)
    -- 工程师名称模糊查询
    AND engineer_name LIKE CONCAT( '%', #{query.name}, '%' )  (如果name不为空)
GROUP BY
    monthly,
    engineer_id
ORDER BY
    monthly DESC
```

## 请求参数详解

### 请求参数类
- **类名**: `EngineerMonthTimelinessQuery`
- **继承**: `PageQuery`（包含分页参数）

### 查询条件字段

| 字段名 | 类型 | 描述 | 是否必填 | 查询方式 |
|--------|------|------|----------|----------|
| startMonth | String | 开始月份(yyyy-MM格式) | 否 | 大于等于查询 |
| endMonth | String | 结束月份(yyyy-MM格式) | 否 | 小于等于查询 |
| name | String | 工程师名称 | 否 | 模糊查询 |
| pageNum | Integer | 页码 | 否 | 分页参数 |
| pageSize | Integer | 每页大小 | 否 | 分页参数 |

## 返回结果详解

### 返回数据结构
- **类型**: `RestResponse<DataGrid<TStatisticsEngineerTimelinessVO>>`
- **说明**: 标准分页响应结构，包含分页信息和统计数据列表

### 返回字段说明

#### TStatisticsEngineerTimelinessVO 字段详解

| 字段名 | 类型 | 描述 | 数据来源 | 计算规则 |
|--------|------|------|----------|----------|
| **基础信息** | | | | |
| monthly | String | 月份 | t_statistics_engineer_timeliness.monthly | 直接查询 |
| engineerName | String | 工程师名称 | t_statistics_engineer_timeliness.engineer_name | 直接查询 |
| engineerId | Long | 工程师ID | t_statistics_engineer_timeliness.engineer_id | 直接查询 |
| **工单统计** | | | | |
| orderNums | Integer | 工单总数 | COUNT(id) | 按月份+工程师分组统计 |
| receiveNum | Integer | 接单数量总和 | SUM(receive_num) | 累计求和 |
| **平均时间指标** | | | | |
| receiveTimeAvg | Double | 平均接单时间(分钟) | ROUND(AVG(IFNULL(receive_time, 0)), 2) | 平均值，保留2位小数 |
| prepareTimeAvg | Double | 平均备料时间(分钟) | ROUND(AVG(IFNULL(prepare_time, 0)), 2) | 平均值，保留2位小数 |
| onRoadTimeAvg | Double | 平均路上时间(分钟) | ROUND(AVG(IFNULL(on_road_time, 0)), 2) | 平均值，保留2位小数 |
| repairTimeAvg | Double | 平均维修时间(分钟) | ROUND(AVG(IFNULL(repair_time, 0)), 2) | 平均值，保留2位小数 |
| confirmTimeAvg | Double | 平均确认时间(分钟) | ROUND(AVG(IFNULL(confirm_time, 0)), 2) | 平均值，保留2位小数 |
| **平均评价指标** | | | | |
| professionalEvaluateAvg | Double | 平均专业评分 | ROUND(AVG(IFNULL(professional_evaluate, 0)), 2) | 平均值，保留2位小数 |
| serviceEvaluateAvg | Double | 平均服务态度评分 | ROUND(AVG(IFNULL(service_evaluate, 0)), 2) | 平均值，保留2位小数 |
| **累计时间指标** | | | | |
| receiveTime | Integer | 接单时间总和(分钟) | SUM(receive_time) | 累计求和 |
| prepareTime | Integer | 备料时间总和(分钟) | SUM(prepare_time) | 累计求和 |
| onRoadTime | Integer | 路上时间总和(分钟) | SUM(on_road_time) | 累计求和 |
| repairTime | Integer | 维修时间总和(分钟) | SUM(repair_time) | 累计求和 |
| confirmTime | Integer | 确认时间总和(分钟) | SUM(confirm_time) | 累计求和 |
| **累计评价指标** | | | | |
| professionalEvaluate | Integer | 专业评分总和 | SUM(professional_evaluate) | 累计求和 |
| serviceEvaluate | Integer | 服务态度评分总和 | SUM(service_evaluate) | 累计求和 |

## 数据源表说明

### 主表：t_statistics_engineer_timeliness（工程师时效统计表）
- **表名**: `t_statistics_engineer_timeliness`
- **主键**: `id`
- **描述**: 存储工程师每个工单的时效统计数据

#### 主要字段说明

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | Long | 主键ID | 自增ID |
| workOrderId | Long | 维修工单ID | 关联工单表 |
| code | String | 维修工单编号 | 工单编码 |
| productId | Long | 产品ID | 关联产品表 |
| engineerId | Long | 工程师ID | 关联工程师表 |
| engineerName | String | 工程师名称 | 冗余字段 |
| monthly | String | 月份 | yyyy-MM格式 |
| name | String | 客户名称 | 冗余字段 |
| receiveTime | Integer | 接单时间(分钟) | 从工单创建到接单的时间 |
| prepareTime | Integer | 备料时间(分钟) | 接单到出发的时间 |
| onRoadTime | Integer | 路上时间(分钟) | 出发到到达的时间 |
| repairTime | Integer | 维修时间(分钟) | 到达到维修完成的时间 |
| confirmTime | Integer | 确认时间(分钟) | 维修完成到客户确认的时间 |
| receiveNum | Integer | 接单数量 | 该工单的接单次数 |
| professionalEvaluate | Integer | 专业评分 | 客户对工程师专业能力的评分 |
| serviceEvaluate | Integer | 服务态度评分 | 客户对工程师服务态度的评分 |
| evaluateTime | LocalDateTime | 评价时间 | 客户评价的时间 |
| createTime | LocalDateTime | 创建时间 | 记录创建时间 |
| updateTime | LocalDateTime | 更新时间 | 记录更新时间 |

## 业务逻辑说明

### 1. 数据聚合规则
- **分组维度**: 按月份(monthly) + 工程师ID(engineer_id)进行分组
- **统计周期**: 支持跨月份统计，通过startMonth和endMonth控制
- **数据来源**: 基于每个工单的时效记录进行聚合计算

### 2. 时间计算逻辑
- **接单时间**: 从工单创建到工程师接单的时间间隔
- **备料时间**: 从接单到工程师出发的时间间隔
- **路上时间**: 从出发到到达客户现场的时间间隔
- **维修时间**: 从到达现场到维修完成的时间间隔
- **确认时间**: 从维修完成到客户确认的时间间隔

### 3. 评分计算逻辑
- **专业评分**: 客户对工程师专业技能的评价分数
- **服务态度评分**: 客户对工程师服务态度的评价分数
- **平均值计算**: 使用ROUND函数保留2位小数，NULL值按0处理

### 4. 查询条件处理
- **月份范围**: 支持开始月份和结束月份的范围查询
- **工程师筛选**: 支持按工程师名称进行模糊查询
- **分页处理**: 使用PageHelper进行分页查询

### 5. 排序规则
- **主排序**: 按月份倒序(monthly DESC)
- **目的**: 最新的月份数据排在前面

## 使用场景

### 1. 工程师绩效考核
- 评估工程师的工作效率和服务质量
- 分析各个环节的时间消耗情况
- 对比不同工程师的表现差异

### 2. 运营数据分析
- 统计月度工单处理情况
- 分析服务时效趋势
- 识别服务流程中的瓶颈环节

### 3. 客户满意度分析
- 监控客户评价趋势
- 分析专业能力和服务态度的评分情况
- 制定服务改进措施

## 使用示例

### 请求示例
```json
{
    "pageNum": 1,
    "pageSize": 20,
    "startMonth": "2024-01",
    "endMonth": "2024-03",
    "name": "张工程师"
}
```

### 响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 50,
        "pageNum": 1,
        "pageSize": 20,
        "pages": 3,
        "list": [
            {
                "monthly": "2024-03",
                "engineerName": "张工程师",
                "engineerId": 123,
                "orderNums": 25,
                "receiveNum": 25,
                "receiveTimeAvg": 15.5,
                "prepareTimeAvg": 30.2,
                "onRoadTimeAvg": 45.8,
                "repairTimeAvg": 120.3,
                "confirmTimeAvg": 10.5,
                "professionalEvaluateAvg": 4.8,
                "serviceEvaluateAvg": 4.9,
                "receiveTime": 388,
                "prepareTime": 755,
                "onRoadTime": 1145,
                "repairTime": 3008,
                "confirmTime": 263,
                "professionalEvaluate": 120,
                "serviceEvaluate": 123
            }
        ]
    }
}
```

## 性能优化建议

### 1. 索引优化
- 在monthly字段上建立索引
- 在engineer_id字段上建立索引
- 考虑建立(monthly, engineer_id)复合索引

### 2. 查询优化
- 合理设置月份查询范围，避免全表扫描
- 使用分页查询，控制返回数据量
- 考虑对历史数据进行归档处理

### 3. 数据维护
- 定期清理过期的统计数据
- 建立数据更新机制，确保统计数据的准确性
- 监控查询性能，及时优化慢查询
